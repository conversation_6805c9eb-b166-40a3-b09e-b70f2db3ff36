'use client';

import { useState, useMemo } from 'react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  CreditCard,
  Shield,
  Upload,
  Plus,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/lib/toast';

interface CreateEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateEmployeeModal({ isOpen, onClose }: CreateEmployeeModalProps) {
  const [currentTab, setCurrentTab] = useState('basic');

  // Basic Information
  const [basicData, setBasicData] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    startDate: '',
  });

  // KYC Information
  const [kycData, setKycData] = useState({
    nationalId: '',
    nationalIdFile: null as File | null,
    addressProof: '',
    addressProofFile: null as File | null,
    dateOfBirth: '',
    nationality: '',
    occupation: '',
  });

  // Bank Information
  const [bankData, setBankData] = useState({
    accountHolderName: '',
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    accountType: '',
    bankAddress: '',
    swiftCode: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Memoized validation checks for tab enabling (without setting errors)
  const isBasicValid = useMemo(() => {
    return (
      basicData.fullName.trim() !== '' &&
      basicData.email.trim() !== '' &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(basicData.email) &&
      basicData.phone.trim() !== '' &&
      basicData.address.trim() !== '' &&
      basicData.startDate.trim() !== ''
    );
  }, [basicData]);

  const isKycValid = useMemo(() => {
    return (
      kycData.nationalId.trim() !== '' &&
      kycData.dateOfBirth.trim() !== '' &&
      kycData.nationality.trim() !== ''
    );
  }, [kycData]);

  const isBankValid = useMemo(() => {
    return (
      bankData.accountHolderName.trim() !== '' &&
      bankData.bankName.trim() !== '' &&
      bankData.accountNumber.trim() !== '' &&
      bankData.routingNumber.trim() !== '' &&
      bankData.accountType.trim() !== ''
    );
  }, [bankData]);

  const handleBasicInputChange = (field: string, value: string) => {
    setBasicData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleKycInputChange = (field: string, value: string) => {
    setKycData((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleBankInputChange = (field: string, value: string) => {
    setBankData((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleFileChange = (field: string, file: File | null) => {
    setKycData((prev) => ({
      ...prev,
      [field]: file,
    }));
  };

  const validateBasicInfo = () => {
    const newErrors: Record<string, string> = {};

    if (!basicData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!basicData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(basicData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!basicData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!basicData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (!basicData.startDate.trim()) {
      newErrors.startDate = 'Start date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateKycInfo = () => {
    const newErrors: Record<string, string> = {};

    if (!kycData.nationalId.trim()) {
      newErrors.nationalId = 'National ID is required';
    }

    if (!kycData.dateOfBirth.trim()) {
      newErrors.dateOfBirth = 'Date of birth is required';
    }

    if (!kycData.nationality.trim()) {
      newErrors.nationality = 'Nationality is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateBankInfo = () => {
    const newErrors: Record<string, string> = {};

    if (!bankData.accountHolderName.trim()) {
      newErrors.accountHolderName = 'Account holder name is required';
    }

    if (!bankData.bankName.trim()) {
      newErrors.bankName = 'Bank name is required';
    }

    if (!bankData.accountNumber.trim()) {
      newErrors.accountNumber = 'Account number is required';
    }

    if (!bankData.routingNumber.trim()) {
      newErrors.routingNumber = 'Routing number is required';
    }

    if (!bankData.accountType.trim()) {
      newErrors.accountType = 'Account type is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (currentTab === 'basic' && validateBasicInfo()) {
      setCurrentTab('kyc');
    } else if (currentTab === 'kyc' && validateKycInfo()) {
      setCurrentTab('bank');
    }
  };

  const handleSubmit = () => {
    if (validateBankInfo()) {
      // Here you would typically send all the data to your API
      const completeData = {
        basic: basicData,
        kyc: kycData,
        bank: bankData,
      };

      console.log('Complete employee data:', completeData);

      toast.success('Employee has been created successfully with all details.');

      // Reset all forms
      setBasicData({
        fullName: '',
        email: '',
        phone: '',
        address: '',
        startDate: '',
      });
      setKycData({
        nationalId: '',
        nationalIdFile: null,
        addressProof: '',
        addressProofFile: null,
        dateOfBirth: '',
        nationality: '',
        occupation: '',
      });
      setBankData({
        accountHolderName: '',
        bankName: '',
        accountNumber: '',
        routingNumber: '',
        accountType: '',
        bankAddress: '',
        swiftCode: '',
      });
      setCurrentTab('basic');
      setErrors({});
      onClose();
    }
  };

  const handleClose = () => {
    setCurrentTab('basic');
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-2xl font-bold">
            <User className="text-primary h-6 w-6" />
            Add New Employee
          </DialogTitle>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Basic Info
            </TabsTrigger>
            <TabsTrigger value="kyc" className="flex items-center gap-2" disabled={!isBasicValid}>
              <Shield className="h-4 w-4" />
              KYC Details
            </TabsTrigger>
            <TabsTrigger
              value="bank"
              className="flex items-center gap-2"
              disabled={!isBasicValid || !isKycValid}
            >
              <CreditCard className="h-4 w-4" />
              Bank Details
            </TabsTrigger>
          </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="mt-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Enter the employee&apos;s basic information to create their account.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {/* Full Name */}
                  <div className="space-y-2">
                    <Label htmlFor="fullName" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Full Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="fullName"
                      value={basicData.fullName}
                      onChange={(e) => handleBasicInputChange('fullName', e.target.value)}
                      placeholder="Enter full name"
                      className={errors.fullName ? 'border-red-500' : ''}
                    />
                    {errors.fullName && <p className="text-sm text-red-500">{errors.fullName}</p>}
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={basicData.email}
                      onChange={(e) => handleBasicInputChange('email', e.target.value)}
                      placeholder="Enter email address"
                      className={errors.email ? 'border-red-500' : ''}
                    />
                    {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                  </div>

                  {/* Phone Number */}
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={basicData.phone}
                      onChange={(e) => handleBasicInputChange('phone', e.target.value)}
                      placeholder="Enter phone number"
                      className={errors.phone ? 'border-red-500' : ''}
                    />
                    {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                  </div>

                  {/* Start Date */}
                  <div className="space-y-2">
                    <Label htmlFor="startDate" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Start Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={basicData.startDate}
                      onChange={(e) => handleBasicInputChange('startDate', e.target.value)}
                      className={errors.startDate ? 'border-red-500' : ''}
                    />
                    {errors.startDate && <p className="text-sm text-red-500">{errors.startDate}</p>}
                  </div>
                </div>

                {/* Address */}
                <div className="space-y-2">
                  <Label htmlFor="address" className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Address <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="address"
                    value={basicData.address}
                    onChange={(e) => handleBasicInputChange('address', e.target.value)}
                    placeholder="Enter complete address"
                    rows={3}
                    className={errors.address ? 'border-red-500' : ''}
                  />
                  {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* KYC Details Tab */}
          <TabsContent value="kyc" className="mt-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  KYC Information
                </CardTitle>
                <CardDescription>
                  Provide identity verification documents and personal details.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="nationalId" className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      National ID Number <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="nationalId"
                      value={kycData.nationalId}
                      onChange={(e) => handleKycInputChange('nationalId', e.target.value)}
                      placeholder="Enter national ID number"
                      className={errors.nationalId ? 'border-red-500' : ''}
                    />
                    {errors.nationalId && (
                      <p className="text-sm text-red-500">{errors.nationalId}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date of Birth <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={kycData.dateOfBirth}
                      onChange={(e) => handleKycInputChange('dateOfBirth', e.target.value)}
                      className={errors.dateOfBirth ? 'border-red-500' : ''}
                    />
                    {errors.dateOfBirth && (
                      <p className="text-sm text-red-500">{errors.dateOfBirth}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nationality" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Nationality <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="nationality"
                      value={kycData.nationality}
                      onChange={(e) => handleKycInputChange('nationality', e.target.value)}
                      placeholder="Enter nationality"
                      className={errors.nationality ? 'border-red-500' : ''}
                    />
                    {errors.nationality && (
                      <p className="text-sm text-red-500">{errors.nationality}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="occupation">Occupation</Label>
                    <Input
                      id="occupation"
                      value={kycData.occupation}
                      onChange={(e) => handleKycInputChange('occupation', e.target.value)}
                      placeholder="Enter occupation"
                    />
                  </div>
                </div>

                {/* Document Upload Section */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold">Document Upload</h4>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    {/* National ID Upload */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Upload className="h-4 w-4" />
                        National ID Document
                      </Label>
                      <div className="hover:border-primary rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors">
                        <Upload className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                        <p className="mb-2 text-sm text-gray-600">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                        <Input
                          type="file"
                          accept=".png,.jpg,.jpeg,.pdf"
                          onChange={(e) =>
                            handleFileChange('nationalIdFile', e.target.files?.[0] || null)
                          }
                          className="hidden"
                          id="nationalIdFile"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="mt-2"
                          onClick={() => document.getElementById('nationalIdFile')?.click()}
                        >
                          Choose File
                        </Button>
                        {kycData.nationalIdFile && (
                          <p className="mt-2 text-sm text-green-600">
                            ✓ {kycData.nationalIdFile.name}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Address Proof Upload */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Upload className="h-4 w-4" />
                        Address Proof Document
                      </Label>
                      <div className="hover:border-primary rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors">
                        <Upload className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                        <p className="mb-2 text-sm text-gray-600">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                        <Input
                          type="file"
                          accept=".png,.jpg,.jpeg,.pdf"
                          onChange={(e) =>
                            handleFileChange('addressProofFile', e.target.files?.[0] || null)
                          }
                          className="hidden"
                          id="addressProofFile"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="mt-2"
                          onClick={() => document.getElementById('addressProofFile')?.click()}
                        >
                          Choose File
                        </Button>
                        {kycData.addressProofFile && (
                          <p className="mt-2 text-sm text-green-600">
                            ✓ {kycData.addressProofFile.name}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Bank Details Tab */}
          <TabsContent value="bank" className="mt-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Bank Account Information
                </CardTitle>
                <CardDescription>
                  Enter bank account details for salary payments and transactions.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="accountHolderName" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Account Holder Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="accountHolderName"
                      value={bankData.accountHolderName}
                      onChange={(e) => handleBankInputChange('accountHolderName', e.target.value)}
                      placeholder="Enter account holder name"
                      className={errors.accountHolderName ? 'border-red-500' : ''}
                    />
                    {errors.accountHolderName && (
                      <p className="text-sm text-red-500">{errors.accountHolderName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bankName" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Bank Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="bankName"
                      value={bankData.bankName}
                      onChange={(e) => handleBankInputChange('bankName', e.target.value)}
                      placeholder="Enter bank name"
                      className={errors.bankName ? 'border-red-500' : ''}
                    />
                    {errors.bankName && <p className="text-sm text-red-500">{errors.bankName}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountNumber" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Account Number <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="accountNumber"
                      value={bankData.accountNumber}
                      onChange={(e) => handleBankInputChange('accountNumber', e.target.value)}
                      placeholder="Enter account number"
                      className={errors.accountNumber ? 'border-red-500' : ''}
                    />
                    {errors.accountNumber && (
                      <p className="text-sm text-red-500">{errors.accountNumber}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="routingNumber" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Routing Number <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="routingNumber"
                      value={bankData.routingNumber}
                      onChange={(e) => handleBankInputChange('routingNumber', e.target.value)}
                      placeholder="Enter routing number"
                      className={errors.routingNumber ? 'border-red-500' : ''}
                    />
                    {errors.routingNumber && (
                      <p className="text-sm text-red-500">{errors.routingNumber}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountType" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Account Type <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={bankData.accountType}
                      onValueChange={(value) => handleBankInputChange('accountType', value)}
                    >
                      <SelectTrigger className={errors.accountType ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select account type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="checking">Checking</SelectItem>
                        <SelectItem value="savings">Savings</SelectItem>
                        <SelectItem value="business">Business</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.accountType && (
                      <p className="text-sm text-red-500">{errors.accountType}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="swiftCode">SWIFT Code</Label>
                    <Input
                      id="swiftCode"
                      value={bankData.swiftCode}
                      onChange={(e) => handleBankInputChange('swiftCode', e.target.value)}
                      placeholder="Enter SWIFT code (if applicable)"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bankAddress">Bank Address</Label>
                  <Textarea
                    id="bankAddress"
                    value={bankData.bankAddress}
                    onChange={(e) => handleBankInputChange('bankAddress', e.target.value)}
                    placeholder="Enter bank address"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex items-center justify-between border-t pt-6">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>

          <div className="flex items-center gap-3">
            {currentTab !== 'basic' && (
              <Button
                variant="outline"
                onClick={() => {
                  if (currentTab === 'bank') setCurrentTab('kyc');
                  else if (currentTab === 'kyc') setCurrentTab('basic');
                }}
              >
                Previous
              </Button>
            )}

            {currentTab === 'basic' && (
              <Button onClick={handleNext} className="bg-primary hover:bg-primary/90">
                Next: KYC Details
              </Button>
            )}

            {currentTab === 'kyc' && (
              <Button onClick={handleNext} className="bg-primary hover:bg-primary/90">
                Next: Bank Details
              </Button>
            )}

            {currentTab === 'bank' && (
              <Button onClick={handleSubmit} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Create Employee
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
