import { EmployeeDetail } from '@/components/employee-detail';

export default async function EmployeeDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  // If employee not found, show a message
  if (!id) {
    return (
      <div className="flex h-full flex-col items-center justify-center py-20">
        <h2 className="mb-4 text-2xl font-bold">Employee Not Found</h2>
        <p className="mb-8 text-gray-600">
          The employee you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission
          to view it.
        </p>
        <button className="bg-primary hover:bg-primary/90 rounded-md px-4 py-2 text-white">
          Back to Employees
        </button>
      </div>
    );
  }

  return <EmployeeDetail employeeId={id} />;
}
